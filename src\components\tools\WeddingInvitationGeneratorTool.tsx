
'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RefreshCw, Printer, Calendar, MapPin, Copy, Save, Loader2 } from 'lucide-react';
import { Textarea } from '../ui/textarea';
import { cn } from '@/lib/utils';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Label } from '../ui/label';
import { useToast } from '@/hooks/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

const FormSchema = z.object({
  template: z.enum(['classic', 'modern', 'elegant']).default('classic'),
  introText: z.string().min(1, 'العبارة الافتتاحية مطلوبة.').default('بكل الحب والتقدير يتشرف'),
  groomName: z.string().min(1, 'اسم العريس مطلوب.'),
  groomFamilyName: z.string().min(1, 'اسم عائلة العريس مطلوب.'),
  brideFamilyName: z.string().min(1, 'اسم عائلة العروس مطلوب.'),
  eventDay: z.string().min(1, 'يوم الحفل مطلوب.').default('يوم السبت'),
  eventDate: z.string().min(1, 'تاريخ الحفل مطلوب.').default('١٣ / ١١ / ١٤٤٥ هـ'),
  eventLocation: z.string().min(1, 'مكان الحفل مطلوب.'),
  closingText: z.string().min(1, 'العبارة الختامية مطلوبة.').default('وبحضوركم يتم لنا الفرح والسرور'),
});

type FormValues = z.infer<typeof FormSchema>;

// Classic Template Component
function ClassicTemplate({ data }: { data: FormValues }) {
    const bgStyle = {
        backgroundImage: "url('data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 11L89 11L89 89L11 89L11 11Z' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M11 11L89 89' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M89 11L11 89' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M50 11L89 50' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M50 11L11 50' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M50 89L11 50' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M50 89L89 50' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M11 50L50 89' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M89 50L50 11' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3C/svg%3E'), linear-gradient(135deg, %23333, %23444)"
    };

    return (
        <div className="w-full max-w-md aspect-[5/7] bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl shadow-2xl flex invitation-preview overflow-hidden border border-gray-700" dir="rtl">
            {/* Main Content Panel - On the right */}
            <div className="w-2/3 p-6 flex flex-col items-center justify-center text-center text-white relative">
                <div className="absolute inset-0 flex items-center justify-center opacity-5 text-9xl text-gray-500 font-serif pointer-events-none" style={{ userSelect: 'none' }}>
                    ﷽
                </div>

                <div className="z-10 flex flex-col h-full w-full">
                    <h1 className="text-3xl sm:text-4xl font-bold my-4 invitation-title tracking-wider text-[#E0C08B]">دعوة خاصة</h1>

                    <p className="text-sm sm:text-base leading-relaxed mb-2">{data.introText}</p>
                    <p className="text-2xl sm:text-3xl font-bold text-[#E0C08B] my-3">{data.groomName}</p>
                    <p className="text-sm sm:text-base leading-relaxed mb-4">{data.brideFamilyName}</p>

                    <div className="my-auto pt-4 space-y-4 text-xs sm:text-sm">
                        {/* Container for Date and Location */}
                        <div className="flex items-center justify-center gap-4">
                            {/* Location - Right Side */}
                            <div className="text-right">
                                <div className="flex items-center justify-end gap-2 font-bold text-[#E0C08B] mb-1">
                                    <span>المكان</span>
                                    <MapPin size={14} />
                                </div>
                                <p className="text-gray-200">{data.eventLocation}</p>
                            </div>

                            {/* Separator */}
                            <div className="border-l border-gray-500 h-12"></div>

                            {/* Date - Left Side */}
                            <div className="text-right">
                                <div className="flex items-center justify-end gap-2 font-bold text-[#E0C08B] mb-1">
                                    <span>التاريخ</span>
                                    <Calendar size={14} />
                                </div>
                                <p className="text-gray-200">{data.eventDay}</p>
                                <p className="text-gray-200" dir="rtl">{data.eventDate}</p>
                            </div>
                        </div>
                        <div className="border border-[#E0C08B] rounded-full px-4 py-2 text-xs sm:text-sm bg-[#E0C08B]/10">
                            {data.closingText}
                        </div>
                    </div>

                    <div className="mt-auto">
                        <p className="text-lg sm:text-xl font-bold text-[#E0C08B]">
                            الداعي / <span className="text-white">{data.groomFamilyName}</span>
                        </p>
                    </div>
                </div>
            </div>

            {/* Decorative Panel - On the left */}
            <div className="w-1/3 bg-cover bg-center opacity-80" style={bgStyle}></div>
      </div>
    );
}

// Modern Template Component
function ModernTemplate({ data }: { data: FormValues }) {
  return (
    <div className="w-full max-w-md aspect-[5/7] bg-gradient-to-br from-rose-50 to-pink-50 rounded-2xl shadow-2xl flex flex-col invitation-preview p-8 text-center text-[#5C5C5C] relative overflow-hidden border border-rose-200" dir="rtl">
      {/* Decorative elements */}
      <div className="absolute top-4 right-4 w-16 h-16 bg-rose-200 rounded-full opacity-20"></div>
      <div className="absolute bottom-4 left-4 w-12 h-12 bg-pink-200 rounded-full opacity-20"></div>
      <div className="absolute top-1/2 left-8 w-8 h-8 bg-rose-300 rounded-full opacity-15"></div>

      <div className="z-10 flex flex-col h-full">
        <h2 className="text-lg tracking-wide mb-4 text-rose-600">{data.introText}</h2>
        <h1 className="text-4xl sm:text-5xl font-bold my-4 text-[#A98E6B] invitation-title">{data.groomName}</h1>
        <p className="text-2xl text-rose-500 my-2">و</p>
        <p className="text-xl mb-6 text-[#5C5C5C]">{data.brideFamilyName}</p>

        <div className="border-y-2 border-[#A98E6B] my-auto py-6 bg-white/50 rounded-lg">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Calendar size={18} className="text-[#A98E6B]" />
            <p className="text-lg font-bold text-[#A98E6B]">{data.eventDay}</p>
          </div>
          <p className="text-lg mb-3" dir="rtl">{data.eventDate}</p>
          <div className="flex items-center justify-center gap-2">
            <MapPin size={18} className="text-[#A98E6B]" />
            <p className="text-base">{data.eventLocation}</p>
          </div>
        </div>

        <p className="mt-auto text-base leading-relaxed">{data.closingText}</p>
        <p className="mt-4 font-bold text-lg text-[#A98E6B]">{data.groomFamilyName}</p>
      </div>
    </div>
  );
}

// Elegant Template Component
function ElegantTemplate({ data }: { data: FormValues }) {
  return (
    <div className="w-full max-w-md aspect-[5/7] bg-gradient-to-b from-amber-50 via-white to-amber-50 rounded-2xl shadow-2xl flex flex-col invitation-preview p-8 text-center text-[#4A4A4A] relative overflow-hidden border-2 border-amber-200" dir="rtl">
      {/* Ornamental border */}
      <div className="absolute inset-4 border-2 border-amber-300 rounded-xl opacity-30"></div>
      <div className="absolute inset-6 border border-amber-400 rounded-lg opacity-20"></div>

      {/* Islamic pattern background */}
      <div className="absolute inset-0 opacity-5">
        <svg className="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="islamic-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
              <circle cx="10" cy="10" r="2" fill="#D97706"/>
              <path d="M10,5 L15,10 L10,15 L5,10 Z" fill="none" stroke="#D97706" strokeWidth="0.5"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#islamic-pattern)"/>
        </svg>
      </div>

      <div className="z-10 flex flex-col h-full">
        {/* Header ornament */}
        <div className="flex justify-center mb-4">
          <div className="w-16 h-1 bg-gradient-to-r from-transparent via-amber-400 to-transparent"></div>
        </div>

        <h1 className="text-2xl font-bold text-amber-700 invitation-title mb-4">بسم الله الرحمن الرحيم</h1>

        <h2 className="text-lg leading-relaxed mb-4 text-amber-600">{data.introText}</h2>

        <div className="bg-amber-100 rounded-lg p-4 my-4 border border-amber-200">
          <h1 className="text-3xl sm:text-4xl font-bold text-amber-800 invitation-title mb-2">{data.groomName}</h1>
          <p className="text-xl text-amber-600 my-2">و</p>
          <p className="text-lg text-amber-700">{data.brideFamilyName}</p>
        </div>

        <div className="my-auto space-y-4">
          <div className="bg-white rounded-lg p-4 shadow-sm border border-amber-200">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Calendar size={16} className="text-amber-600" />
              <p className="font-bold text-amber-700">{data.eventDay}</p>
            </div>
            <p className="text-amber-600" dir="rtl">{data.eventDate}</p>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border border-amber-200">
            <div className="flex items-center justify-center gap-2 mb-2">
              <MapPin size={16} className="text-amber-600" />
              <p className="font-bold text-amber-700">المكان</p>
            </div>
            <p className="text-amber-600">{data.eventLocation}</p>
          </div>
        </div>

        <div className="mt-auto">
          <div className="bg-amber-50 rounded-lg p-3 border border-amber-200 mb-4">
            <p className="text-sm leading-relaxed text-amber-700">{data.closingText}</p>
          </div>
          <p className="font-bold text-lg text-amber-800">{data.groomFamilyName}</p>
        </div>

        {/* Footer ornament */}
        <div className="flex justify-center mt-4">
          <div className="w-16 h-1 bg-gradient-to-r from-transparent via-amber-400 to-transparent"></div>
        </div>
      </div>
    </div>
  );
}

export function WeddingInvitationGeneratorTool() {
  const [isLoading, setIsLoading] = useState(false);
  const [savedTemplates, setSavedTemplates] = useState<FormValues[]>([]);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    mode: 'onChange',
    defaultValues: {
        template: 'classic',
        introText: 'بكل الحب والتقدير يتشرف',
        groomName: 'خالد',
        groomFamilyName: 'محمد بن عبدالله الأحمد وأبنائه',
        brideFamilyName: 'كريمة الشيخ / صالح المزيد',
        eventDay: 'يوم السبت',
        eventDate: '١٣ / ١١ / ١٤٤٥ هـ',
        eventLocation: 'قاعة ليلتي للاحتفالات بالرياض',
        closingText: 'وبحضوركم يتم لنا الفرح والسرور',
    },
  });

  const formData = form.watch();

  // Load saved templates from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('wedding-invitation-templates');
      if (saved) {
        try {
          setSavedTemplates(JSON.parse(saved));
        } catch (error) {
          console.error('Error loading saved templates:', error);
        }
      }
    }
  }, []);

  const handlePrint = async () => {
    setIsLoading(true);
    try {
      // Small delay to ensure the UI updates
      await new Promise(resolve => setTimeout(resolve, 100));
      window.print();
      toast({
        title: "جاهز للطباعة",
        description: "تم تحضير الدعوة للطباعة بنجاح.",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "خطأ في الطباعة",
        description: "حدث خطأ أثناء تحضير الدعوة للطباعة.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveTemplate = () => {
    const newTemplate = { ...formData };
    const updatedTemplates = [...savedTemplates, newTemplate];
    setSavedTemplates(updatedTemplates);
    localStorage.setItem('wedding-invitation-templates', JSON.stringify(updatedTemplates));
    toast({
      title: "تم حفظ القالب",
      description: "تم حفظ قالب الدعوة بنجاح.",
    });
  };

  const copyInvitationText = () => {
    const invitationText = `${formData.introText}
${formData.groomName}
${formData.brideFamilyName}

${formData.eventDay}
${formData.eventDate}
${formData.eventLocation}

${formData.closingText}
${formData.groomFamilyName}`;

    navigator.clipboard.writeText(invitationText).then(() => {
      toast({
        title: "تم النسخ",
        description: "تم نسخ نص الدعوة إلى الحافظة.",
      });
    }).catch(() => {
      toast({
        variant: "destructive",
        title: "خطأ في النسخ",
        description: "لم نتمكن من نسخ النص.",
      });
    });
  };

  const resetForm = () => {
    form.reset({
        template: 'classic',
        introText: 'بكل الحب والتقدير يتشرف',
        groomName: '',
        groomFamilyName: '',
        brideFamilyName: '',
        eventDay: 'يوم السبت',
        eventDate: '',
        eventLocation: '',
        closingText: 'وبحضوركم يتم لنا الفرح والسرور',
    });
    toast({
      title: "تم إعادة تعيين النموذج",
      description: "تم مسح جميع البيانات بنجاح.",
    });
  };

  const renderTemplate = () => {
    switch (formData.template) {
      case 'modern':
        return <ModernTemplate data={formData} />;
      case 'elegant':
        return <ElegantTemplate data={formData} />;
      default:
        return <ClassicTemplate data={formData} />;
    }
  };

  return (
    <>
        <style jsx global>{`
            @import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Tajawal:wght@400;500;700&family=Scheherazade+New:wght@400;700&display=swap');

            .invitation-preview {
                font-family: 'Tajawal', sans-serif;
            }
            .invitation-title {
                font-family: 'Amiri', serif;
            }
            @media print {
                body * {
                    visibility: hidden;
                }
                .printable-area, .printable-area * {
                    visibility: visible;
                }
                .printable-area {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    margin: 0;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .invitation-preview {
                    transform: scale(1.2);
                }
                @page {
                    size: A5 portrait;
                    margin: 0;
                }
            }
        `}</style>

        <div className="flex flex-col items-center gap-8 w-full max-w-6xl mx-auto">
            {/* Form validation errors alert */}
            {Object.keys(form.formState.errors).length > 0 && (
                <Alert variant="destructive" className="print:hidden">
                    <AlertDescription>
                        يرجى تصحيح الأخطاء في النموذج قبل المتابعة.
                    </AlertDescription>
                </Alert>
            )}

            <Card className="w-full print:hidden">
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>بيانات الدعوة</span>
                        <div className="flex gap-2">
                            <Button variant="outline" size="sm" onClick={copyInvitationText}>
                                <Copy className="ml-2 h-4 w-4" />
                                نسخ النص
                            </Button>
                            <Button variant="outline" size="sm" onClick={saveTemplate}>
                                <Save className="ml-2 h-4 w-4" />
                                حفظ القالب
                            </Button>
                            <Button variant="ghost" size="sm" onClick={resetForm}>
                                <RefreshCw className="ml-2 h-4 w-4" />
                                إعادة تعيين
                            </Button>
                        </div>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form className="space-y-6">
                            <FormField
                              control={form.control}
                              name="template"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>اختر تصميم الدعوة</FormLabel>
                                  <FormControl>
                                    <RadioGroup
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                      className="grid grid-cols-3 gap-4"
                                      dir="rtl"
                                    >
                                      <Label className={cn("border rounded-md p-3 flex items-center justify-end gap-2 cursor-pointer hover:bg-muted/50 transition-colors", field.value === 'classic' && "bg-primary/10 border-primary")}>
                                        <span>قالب كلاسيكي</span>
                                        <RadioGroupItem value="classic" />
                                      </Label>
                                      <Label className={cn("border rounded-md p-3 flex items-center justify-end gap-2 cursor-pointer hover:bg-muted/50 transition-colors", field.value === 'modern' && "bg-primary/10 border-primary")}>
                                        <span>قالب حديث</span>
                                        <RadioGroupItem value="modern" />
                                      </Label>
                                      <Label className={cn("border rounded-md p-3 flex items-center justify-end gap-2 cursor-pointer hover:bg-muted/50 transition-colors", field.value === 'elegant' && "bg-primary/10 border-primary")}>
                                        <span>قالب أنيق</span>
                                        <RadioGroupItem value="elegant" />
                                      </Label>
                                    </RadioGroup>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                    <FormField control={form.control} name="introText" render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>العبارة الافتتاحية</FormLabel>
                                            <FormControl>
                                                <Textarea {...field} className="min-h-[80px]" dir="rtl" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />

                                    <FormField control={form.control} name="groomName" render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>اسم العريس</FormLabel>
                                            <FormControl>
                                                <Input placeholder="مثال: خالد" {...field} dir="rtl" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />

                                    <FormField control={form.control} name="groomFamilyName" render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>الداعي (اسم عائلة العريس)</FormLabel>
                                            <FormControl>
                                                <Input placeholder="مثال: محمد بن عبدالله الأحمد وأبنائه" {...field} dir="rtl" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />

                                    <FormField control={form.control} name="brideFamilyName" render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>اسم عائلة العروس</FormLabel>
                                            <FormControl>
                                                <Input placeholder="مثال: كريمة الشيخ / صالح المزيد" {...field} dir="rtl" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                </div>

                                <div className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4">
                                        <FormField control={form.control} name="eventDay" render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>اليوم</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="يوم السبت" {...field} dir="rtl" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />
                                        <FormField control={form.control} name="eventDate" render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>التاريخ</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="١٣ / ١١ / ١٤٤٥ هـ" {...field} dir="rtl" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />
                                    </div>

                                    <FormField control={form.control} name="eventLocation" render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>المكان</FormLabel>
                                            <FormControl>
                                                <Input placeholder="قاعة الاحتفالات" {...field} dir="rtl" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />

                                    <FormField control={form.control} name="closingText" render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>العبارة الختامية</FormLabel>
                                            <FormControl>
                                                <Textarea {...field} className="min-h-[80px]" dir="rtl" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                </div>
                            </div>

                            <div className="flex gap-4 pt-4">
                                <Button
                                    onClick={handlePrint}
                                    className="flex-1"
                                    type="button"
                                    disabled={isLoading || Object.keys(form.formState.errors).length > 0}
                                >
                                    {isLoading ? (
                                        <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                                    ) : (
                                        <Printer className="ml-2 h-4 w-4" />
                                    )}
                                    طباعة الدعوة / حفظ كـ PDF
                                </Button>
                                <Button
                                    onClick={copyInvitationText}
                                    variant="outline"
                                    type="button"
                                    disabled={Object.keys(form.formState.errors).length > 0}
                                >
                                    <Copy className="ml-2 h-4 w-4" />
                                    نسخ النص
                                </Button>
                            </div>
                        </form>
                    </Form>
                </CardContent>
            </Card>

            <div className="w-full flex items-center justify-center printable-area">
                {renderTemplate()}
            </div>
        </div>
    </>
  );
}
