import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { WeddingInvitationGeneratorTool } from '@/components/tools/WeddingInvitationGeneratorTool';
import { useToast } from '@/hooks/use-toast';

// Mock the useToast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: jest.fn(),
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock window.print
Object.defineProperty(window, 'print', {
  value: jest.fn(),
});

// Mock navigator.clipboard
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: jest.fn().mockResolvedValue(undefined),
  },
});

describe('WeddingInvitationGeneratorTool', () => {
  const mockToast = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useToast as jest.Mock).mockReturnValue({ toast: mockToast });
    localStorageMock.getItem.mockReturnValue(null);
  });

  it('renders the wedding invitation generator form', () => {
    render(<WeddingInvitationGeneratorTool />);
    
    expect(screen.getByText('بيانات الدعوة')).toBeInTheDocument();
    expect(screen.getByText('اختر تصميم الدعوة')).toBeInTheDocument();
    expect(screen.getByText('قالب كلاسيكي')).toBeInTheDocument();
    expect(screen.getByText('قالب حديث')).toBeInTheDocument();
    expect(screen.getByText('قالب أنيق')).toBeInTheDocument();
  });

  it('displays default values in form fields', () => {
    render(<WeddingInvitationGeneratorTool />);
    
    expect(screen.getByDisplayValue('بكل الحب والتقدير يتشرف')).toBeInTheDocument();
    expect(screen.getByDisplayValue('خالد')).toBeInTheDocument();
    expect(screen.getByDisplayValue('محمد بن عبدالله الأحمد وأبنائه')).toBeInTheDocument();
    expect(screen.getByDisplayValue('كريمة الشيخ / صالح المزيد')).toBeInTheDocument();
    expect(screen.getByDisplayValue('يوم السبت')).toBeInTheDocument();
    expect(screen.getByDisplayValue('١٣ / ١١ / ١٤٤٥ هـ')).toBeInTheDocument();
    expect(screen.getByDisplayValue('قاعة ليلتي للاحتفالات بالرياض')).toBeInTheDocument();
    expect(screen.getByDisplayValue('وبحضوركم يتم لنا الفرح والسرور')).toBeInTheDocument();
  });

  it('switches between templates correctly', () => {
    render(<WeddingInvitationGeneratorTool />);
    
    // Default should be classic template
    expect(screen.getByDisplayValue('classic')).toBeChecked();
    
    // Switch to modern template
    fireEvent.click(screen.getByLabelText('قالب حديث'));
    expect(screen.getByDisplayValue('modern')).toBeChecked();
    
    // Switch to elegant template
    fireEvent.click(screen.getByLabelText('قالب أنيق'));
    expect(screen.getByDisplayValue('elegant')).toBeChecked();
  });

  it('validates required fields', async () => {
    render(<WeddingInvitationGeneratorTool />);
    
    // Clear a required field
    const groomNameInput = screen.getByLabelText('اسم العريس');
    fireEvent.change(groomNameInput, { target: { value: '' } });
    
    // Try to print (should be disabled due to validation errors)
    const printButton = screen.getByText('طباعة الدعوة / حفظ كـ PDF');
    expect(printButton).toBeDisabled();
  });

  it('handles print functionality', async () => {
    render(<WeddingInvitationGeneratorTool />);
    
    const printButton = screen.getByText('طباعة الدعوة / حفظ كـ PDF');
    fireEvent.click(printButton);
    
    await waitFor(() => {
      expect(window.print).toHaveBeenCalled();
      expect(mockToast).toHaveBeenCalledWith({
        title: "جاهز للطباعة",
        description: "تم تحضير الدعوة للطباعة بنجاح.",
      });
    });
  });

  it('handles copy text functionality', async () => {
    render(<WeddingInvitationGeneratorTool />);
    
    const copyButton = screen.getByText('نسخ النص');
    fireEvent.click(copyButton);
    
    await waitFor(() => {
      expect(navigator.clipboard.writeText).toHaveBeenCalled();
      expect(mockToast).toHaveBeenCalledWith({
        title: "تم النسخ",
        description: "تم نسخ نص الدعوة إلى الحافظة.",
      });
    });
  });

  it('handles save template functionality', () => {
    render(<WeddingInvitationGeneratorTool />);
    
    const saveButton = screen.getByText('حفظ القالب');
    fireEvent.click(saveButton);
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'wedding-invitation-templates',
      expect.any(String)
    );
    expect(mockToast).toHaveBeenCalledWith({
      title: "تم حفظ القالب",
      description: "تم حفظ قالب الدعوة بنجاح.",
    });
  });

  it('handles reset form functionality', () => {
    render(<WeddingInvitationGeneratorTool />);
    
    // Change a field value
    const groomNameInput = screen.getByLabelText('اسم العريس');
    fireEvent.change(groomNameInput, { target: { value: 'أحمد' } });
    expect(groomNameInput).toHaveValue('أحمد');
    
    // Reset the form
    const resetButton = screen.getByText('إعادة تعيين');
    fireEvent.click(resetButton);
    
    // Field should be cleared
    expect(groomNameInput).toHaveValue('');
    expect(mockToast).toHaveBeenCalledWith({
      title: "تم إعادة تعيين النموذج",
      description: "تم مسح جميع البيانات بنجاح.",
    });
  });

  it('loads saved templates from localStorage', () => {
    const savedTemplates = [
      {
        template: 'classic' as const,
        introText: 'نص محفوظ',
        groomName: 'محمد',
        groomFamilyName: 'العائلة',
        brideFamilyName: 'عائلة العروس',
        eventDay: 'الجمعة',
        eventDate: '١٥ / ١٢ / ١٤٤٥',
        eventLocation: 'الرياض',
        closingText: 'نص ختامي',
      },
    ];
    
    localStorageMock.getItem.mockReturnValue(JSON.stringify(savedTemplates));
    
    render(<WeddingInvitationGeneratorTool />);
    
    expect(localStorageMock.getItem).toHaveBeenCalledWith('wedding-invitation-templates');
  });

  it('handles localStorage errors gracefully', () => {
    localStorageMock.getItem.mockImplementation(() => {
      throw new Error('localStorage error');
    });
    
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    
    render(<WeddingInvitationGeneratorTool />);
    
    expect(consoleSpy).toHaveBeenCalledWith('Error loading saved templates:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });

  it('displays validation errors when form is invalid', async () => {
    render(<WeddingInvitationGeneratorTool />);
    
    // Clear required fields
    fireEvent.change(screen.getByLabelText('اسم العريس'), { target: { value: '' } });
    fireEvent.change(screen.getByLabelText('اسم عائلة العريس'), { target: { value: '' } });
    
    // Trigger validation by trying to submit
    fireEvent.blur(screen.getByLabelText('اسم العريس'));
    
    await waitFor(() => {
      expect(screen.getByText('يرجى تصحيح الأخطاء في النموذج قبل المتابعة.')).toBeInTheDocument();
    });
  });

  it('renders invitation preview correctly', () => {
    render(<WeddingInvitationGeneratorTool />);
    
    // Check if the invitation preview is rendered
    expect(screen.getByText('دعوة خاصة')).toBeInTheDocument();
    expect(screen.getByText('خالد')).toBeInTheDocument();
    expect(screen.getByText('محمد بن عبدالله الأحمد وأبنائه')).toBeInTheDocument();
  });
});
